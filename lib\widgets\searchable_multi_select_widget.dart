import 'package:flutter/material.dart';

/// A widget that allows searching and selecting multiple items from a list
class SearchableMultiSelectWidget extends StatefulWidget {
  final List<String> items;
  final List<String> selectedItems;
  final Function(List<String>) onSelectionChanged;
  final String labelText;
  final String hintText;
  final String searchHintText;
  final IconData? prefixIcon;
  final bool isRequired;
  final String? Function(List<String>?)? validator;
  final bool allowAddNew;
  final Function(String)? onAddNew;
  final String addNewButtonText;
  final bool isRTL;

  const SearchableMultiSelectWidget({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onSelectionChanged,
    required this.labelText,
    required this.hintText,
    required this.searchHintText,
    this.prefixIcon,
    this.isRequired = false,
    this.validator,
    this.allowAddNew = false,
    this.onAddNew,
    this.addNewButtonText = 'Add New',
    this.isRTL = false,
  });

  @override
  State<SearchableMultiSelectWidget> createState() => _SearchableMultiSelectWidgetState();
}

class _SearchableMultiSelectWidgetState extends State<SearchableMultiSelectWidget> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _newItemController = TextEditingController();
  bool _isExpanded = false;
  bool _isAddingNew = false;
  List<String> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.items);
    _searchController.addListener(_filterItems);
  }

  @override
  void didUpdateWidget(SearchableMultiSelectWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _filteredItems = List.from(widget.items);
      _filterItems();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _newItemController.dispose();
    super.dispose();
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = widget.items
          .where((item) => item.toLowerCase().contains(query))
          .toList();
    });
  }

  void _toggleSelection(String item) {
    final newSelection = List<String>.from(widget.selectedItems);
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    widget.onSelectionChanged(newSelection);
  }

  void _addNewItem() {
    final newItem = _newItemController.text.trim();
    if (newItem.isNotEmpty && !widget.items.contains(newItem)) {
      widget.onAddNew?.call(newItem);
      _newItemController.clear();
      setState(() {
        _isAddingNew = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return FormField<List<String>>(
      initialValue: widget.selectedItems,
      validator: widget.validator,
      builder: (FormFieldState<List<String>> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main input field
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: widget.labelText,
                  border: const OutlineInputBorder(),
                  prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
                  suffixIcon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  errorText: field.errorText,
                ),
                child: widget.selectedItems.isEmpty
                    ? Text(
                        widget.hintText,
                        style: TextStyle(color: Colors.grey[600]),
                      )
                    : Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: widget.selectedItems.map((item) {
                          return Chip(
                            label: Text(
                              item,
                              style: const TextStyle(fontSize: 12),
                            ),
                            deleteIcon: const Icon(Icons.close, size: 16),
                            onDeleted: () => _toggleSelection(item),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                            labelStyle: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          );
                        }).toList(),
                      ),
              ),
            ),

            // Expanded selection area
            if (_isExpanded) ...[
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    // Search field
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: widget.searchHintText,
                          prefixIcon: const Icon(Icons.search),
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    ),

                    // Add new item section
                    if (widget.allowAddNew) ...[
                      if (_isAddingNew) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _newItemController,
                                  decoration: InputDecoration(
                                    hintText: widget.isRTL ? 'أدخل عنصر جديد' : 'Enter new item',
                                    border: const OutlineInputBorder(),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  ),
                                  onSubmitted: (_) => _addNewItem(),
                                ),
                              ),
                              IconButton(
                                onPressed: _addNewItem,
                                icon: const Icon(Icons.add),
                              ),
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    _isAddingNew = false;
                                    _newItemController.clear();
                                  });
                                },
                                icon: const Icon(Icons.close),
                              ),
                            ],
                          ),
                        ),
                      ] else ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: SizedBox(
                            width: double.infinity,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  _isAddingNew = true;
                                });
                              },
                              icon: const Icon(Icons.add),
                              label: Text(widget.addNewButtonText),
                            ),
                          ),
                        ),
                      ],
                      const Divider(height: 1),
                    ],

                    // Items list
                    Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = _filteredItems[index];
                          final isSelected = widget.selectedItems.contains(item);
                          
                          return CheckboxListTile(
                            title: Text(item),
                            value: isSelected,
                            onChanged: (_) => _toggleSelection(item),
                            dense: true,
                            controlAffinity: widget.isRTL 
                                ? ListTileControlAffinity.leading 
                                : ListTileControlAffinity.trailing,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
